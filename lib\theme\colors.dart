import 'package:flutter/material.dart';

/// [AppColors]通过AppColors访问固定颜色表
/// [ThemeColorsExtension]通过context访问当前主题选择合适的颜色

/// 颜色表
class AppColors {
  // 主色调
  static const Color primary = Color(0xFF1887FF);
  static const Color primaryHove = Color(0xFF469FFF);
  static const Color primaryPressed = Color(0xFF166BD5);
  static const Color primaryDisabled = Color(0xFFA3CFFF);

  // 辅助色
  static const Color cyan = Color(0xFF00C1DE);
  static const Color yellow = Color(0xFFFACE00);
  static const Color accent = Color(0xFFE43958);
  static const Color purple = Color(0xFF643CFF);
  static const Color white = Color(0xFFFFFFFF);

  // 功能色
  static const Color success = Color(0xFF40C04C);
  static const Color warning = Color(0xFFFF9500);
  static const Color error = Color(0xFFFF3B30);
  static const Color info = Color(0xFF909399);

  /// 背景色 - 亮色
  static const Color background100 = Color(0xFFFAFAFA);
  static const Color background200 = Color(0xFFF5F6F9);
  static const Color background300 = Color(0xFFF0F3FA);

  /// 背景色 - 暗色
  static const Color backgroundDark100 = Color(0xFF121212);
  static const Color backgroundDark200 = Color(0xFF1E1E1E);
  static const Color backgroundDark300 = Color(0xFF2C2C2C);

  /// 边框色 - 亮色
  static const Color border100 = Color(0xFFF1F1F1);
  static const Color border200 = Color(0xFFEAEAEA);
  static const Color border300 = Color(0xFFE2E2E2);

  /// 边框色 - 暗色
  static const Color borderDark100 = Color(0xFF2A2A2A);
  static const Color borderDark200 = Color(0xFF3A3A3A);
  static const Color borderDark300 = Color(0xFF505050);

  /// 文字色 - 亮色
  static const Color textPrimary = Color(0xFF141414);
  static const Color textSecondary = Color(0xFF707070);
  static const Color textHint = Color(0xFFD0D0D0);
  static const Color textDisabled = Color(0xFF999999);
  static const Color textWhite = Color(0xFFFFFFFF);

  /// 文字色 - 暗色
  static const Color textPrimaryDark = Color(0xFFE5EAF3);
  static const Color textSecondaryDark = Color(0xFFAAAAAA);

  /// icon - 亮色
  static const Color icon100 = Color(0xFFB7BDC3);
  static const Color icon200 = Color(0xFF939CA5);
  static const Color icon300 = Color(0xFF4B5A69);

  /// icon - 暗色
  static const Color iconDark100 = Color(0xFF666666);
  static const Color iconDark200 = Color(0xFF888888);
  static const Color iconDark300 = Color(0xFFAAAAAA);

  // 将自定义颜色转换为MaterialColor
  static MaterialColor createMaterialColor(Color color) {
    List<double> strengths = <double>[.05, .1, .2, .3, .4, .5, .6, .7, .8, .9];
    Map<int, Color> swatch = <int, Color>{};
    final double r = color.r, g = color.g, b = color.b;

    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        (r + ((ds < 0 ? r : (255 - r)) * ds)).round(),
        (g + ((ds < 0 ? g : (255 - g)) * ds)).round(),
        (b + ((ds < 0 ? b : (255 - b)) * ds)).round(),
        1,
      );
    }
    return MaterialColor(color.toARGB32(), swatch);
  }
}

/// 引入文件通过context访问 - 根据当前主题选择合适的颜色
extension ThemeColorsExtension on BuildContext {
  // 当前主题
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;

  // 背景色
  Color get backgroundWhite =>
      isDarkMode ? AppColors.backgroundDark300 : AppColors.white;
  Color get background100 =>
      isDarkMode ? AppColors.backgroundDark100 : AppColors.background100;
  Color get background200 =>
      isDarkMode ? AppColors.backgroundDark200 : AppColors.background200;
  Color get background300 =>
      isDarkMode ? AppColors.backgroundDark300 : AppColors.background300;

  // 边框色
  Color get border100 =>
      isDarkMode ? AppColors.borderDark100 : AppColors.border100;
  Color get border200 =>
      isDarkMode ? AppColors.borderDark200 : AppColors.border200;
  Color get border300 =>
      isDarkMode ? AppColors.borderDark300 : AppColors.border300;

  // 文本色
  Color get textPrimary =>
      isDarkMode ? AppColors.textPrimaryDark : AppColors.textPrimary;
  Color get textSecondary =>
      isDarkMode ? AppColors.textSecondaryDark : AppColors.textSecondary;
  Color get textHint => AppColors.textHint;

  // icon
  Color get icon100 => isDarkMode ? AppColors.iconDark100 : AppColors.icon100;
  Color get icon200 => isDarkMode ? AppColors.iconDark200 : AppColors.icon200;
  Color get icon300 => isDarkMode ? AppColors.iconDark300 : AppColors.icon300;

  // 特殊场景色
  Color get activeGrayColor => isDarkMode
      ? AppColors.white.withValues(alpha: 0.25)
      : AppColors.background300;
  Color get activeWhiteColor =>
      isDarkMode ? AppColors.white.withValues(alpha: 0.25) : AppColors.white;

  /// 窗口栏
  Color get windowBarColor =>
      isDarkMode ? AppColors.backgroundDark100 : AppColors.background300;
}
