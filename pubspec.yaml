name: jyt_components_package
description: "A new Flutter package project."
version: 0.0.1
homepage: https://github.com/yourusername/custom_button

environment:
  sdk: ^3.8.1
  flutter: ">=1.17.0"

dependencies:
  cached_network_image: ^3.3.1
  crop_image: ^1.0.16
  equatable: ^2.0.7
  file_picker: ^10.1.9
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.3.1
  get: ^4.7.2
  go_router: ^15.1.3
  intl: ^0.20.2
  json_annotation: ^4.9.0
  shared_preferences: ^2.5.3
  syncfusion_flutter_datepicker: ^31.1.17
  window_manager: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
