import 'package:jyt_components_package/components/models/enterprise/enterprise_detail.dart';
import 'package:jyt_components_package/components/models/user/user_credential.dart';
import 'package:jyt_components_package/components/models/user/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get_utils/get_utils.dart';
import 'dart:convert';

/// 存储键常量
class StorageKeys {
  static const String userInfo = 'user_info'; // 用户信息
  static const String isLogin = 'is_login'; // 登录状态
  static const String token = 'token'; // token
  static const String userCredential = 'user_credential'; // 用户凭证
  static const String themeMode = 'theme_mode'; // 主题模式
  static const String loginPageInfo = 'login_page_info'; // 登录页企业信息
  static const String localMessages = 'local_messages'; // 本地消息
  static const String filePaths = 'file_path'; // 文件路径
}

/// 全局Storage统一管理
class StorageUtil {
  static SharedPreferences? _prefsInstance;
  static bool _initialized = false;

  // 初始化方法，可以在应用启动时调用
  static Future<void> init() async {
    if (!_initialized) {
      _prefsInstance = await SharedPreferences.getInstance();
      _initialized = true;
    }
  }

  // 获取SharedPreferences实例
  static Future<SharedPreferences> get _prefs async {
    if (!_initialized) {
      await init();
    }
    return _prefsInstance!;
  }

  // 删除指定键
  static Future<bool> remove(String key) async {
    final prefs = await _prefs;
    return prefs.remove(key);
  }

  // 清除所有数据
  static Future<bool> clear() async {
    final prefs = await _prefs;
    return prefs.clear();
  }

  // 用户信息
  static Future<bool> setUserInfo(UserInfo userInfo) async {
    final prefs = await _prefs;
    return prefs.setString(StorageKeys.userInfo, jsonEncode(userInfo.toJson()));
  }

  static Future<UserInfo?> getUserInfo() async {
    final prefs = await _prefs;
    final userString = prefs.getString(StorageKeys.userInfo);
    if (userString == null || userString.isEmpty) return null;

    try {
      final userJson = jsonDecode(userString);
      return UserInfo.fromJson(userJson);
    } catch (e) {
      print('解析用户信息失败: $e');
      return null;
    }
  }

  static Future<bool> removeUserInfo() {
    return remove(StorageKeys.userInfo);
  }

  // 登录状态
  static Future<bool> setLoginStatus(bool isLogin) async {
    final prefs = await _prefs;
    return prefs.setBool(StorageKeys.isLogin, isLogin);
  }

  static Future<bool> getLoginStatus() async {
    final prefs = await _prefs;
    return prefs.getBool(StorageKeys.isLogin) ?? false;
  }

  static Future<bool> removeLoginStatus() {
    return remove(StorageKeys.isLogin);
  }

  // Token
  static Future<bool> setToken(String token) async {
    final prefs = await _prefs;
    return prefs.setString(StorageKeys.token, token);
  }

  static Future<String?> getToken() async {
    final prefs = await _prefs;
    return prefs.getString(StorageKeys.token);
  }

  static Future<bool> removeToken() {
    return remove(StorageKeys.token);
  }

  // 用户凭证
  static Future<bool> setUserCredential(UserCredential userCredential) async {
    final prefs = await _prefs;
    return prefs.setString(
      StorageKeys.userCredential,
      jsonEncode(userCredential.toJson()),
    );
  }

  static Future<UserCredential> getUserCredential() async {
    final prefs = await _prefs;
    final userCredentialString = prefs.getString(StorageKeys.userCredential);
    if (userCredentialString == null || userCredentialString.isEmpty) {
      return UserCredential();
    }

    final userCredential = jsonDecode(userCredentialString);
    return UserCredential.fromJson(userCredential);
  }

  static Future<bool> removeUserCredential() {
    return remove(StorageKeys.userCredential);
  }

  // 主题模式
  static Future<bool> setThemeMode(String themeMode) async {
    final prefs = await _prefs;
    return prefs.setString(StorageKeys.themeMode, themeMode);
  }

  static Future<String> getThemeMode() async {
    final prefs = await _prefs;
    return prefs.getString(StorageKeys.themeMode) ?? 'system';
  }

  static Future<bool> removeThemeMode() {
    return remove(StorageKeys.themeMode);
  }

  // 登录页企业信息
  static Future<bool> setLoginPageInfo(EnterpriseDetail loginPageInfo) async {
    final prefs = await _prefs;
    return prefs.setString(
      StorageKeys.loginPageInfo,
      jsonEncode(loginPageInfo.toJson()),
    );
  }

  static Future<EnterpriseDetail> getLoginPageInfo() async {
    final prefs = await _prefs;
    final loginPageInfoString = prefs.getString(StorageKeys.loginPageInfo);
    if (loginPageInfoString == null || loginPageInfoString.isEmpty) {
      return EnterpriseDetail();
    }

    final loginPageInfo = jsonDecode(loginPageInfoString);
    return EnterpriseDetail.fromJson(loginPageInfo);
  }

  static Future<bool> removeLoginPageInfo() {
    return remove(StorageKeys.loginPageInfo);
  }

  // 本地消息
  static Future<void> setLocalMessages(String message) async {
    final prefs = await _prefs;
    final messages = prefs.getStringList(StorageKeys.localMessages) ?? [];
    messages.add(message);
    await prefs.setStringList(StorageKeys.localMessages, messages);
  }

  static Future<List<String>> getLocalMessages() async {
    final prefs = await _prefs;
    return prefs.getStringList(StorageKeys.localMessages) ?? [];
  }

  static Future<bool> removeLocalMessages() {
    return remove(StorageKeys.localMessages);
  }

  // 文件路径
  static Future<bool> setFilePath(String filePath) async {
    final prefs = await _prefs;
    final filePaths = prefs.getStringList(StorageKeys.filePaths) ?? [];
    var newPath = jsonDecode(filePath);
    var oldPath = filePaths.firstWhereOrNull(
      (k) => k.contains(newPath['fileId']),
    );
    if (oldPath != null) filePaths.remove(oldPath);
    filePaths.add(filePath);
    return prefs.setStringList(StorageKeys.filePaths, filePaths);
  }

  static Future<List<String>> getFilePath() async {
    final prefs = await _prefs;
    return prefs.getStringList(StorageKeys.filePaths) ?? [];
  }

  static Future<bool> removeFilePath() {
    return remove(StorageKeys.filePaths);
  }
}
