import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class AppDatetimePicker extends StatefulWidget {
  const AppDatetimePicker({
    super.key,
    required this.onSelecting,
    required this.limitMinDate,
    required this.limitMaxDate,
    required this.isSingle,
    this.limitRange,
    required this.onSubmited,
    required this.firstTitle,
    required this.lastTitle,
  });
  final Function(bool) onSelecting;
  final Function(PickerDateRange) onSubmited;
  final String firstTitle;
  final String lastTitle;
  final DateTime limitMinDate;
  final DateTime limitMaxDate;
  final PickerDateRange? limitRange;
  final bool isSingle;

  @override
  State<AppDatetimePicker> createState() => _DateTimeSelectorState();
}

class _DateTimeSelectorState extends State<AppDatetimePicker> {
  late bool _isSelectedLeft;
  bool _isSelectingDate = false;
  bool _isVerify = true;
  late DateTime _minDate;
  late DateTime _maxDate;
  PickerDateRange? _initSelectRange;
  String _startDate = '';
  String _startTime = '';
  String _endDate = '';
  String _endTime = '';
  OverlayEntry? _overlayEntry;
  final GlobalKey _startTimeKey = GlobalKey();
  final GlobalKey _endTimeKey = GlobalKey();

  late TextEditingController _startDateEditingController;
  late TextEditingController _endDateEditingController;
  late TextEditingController _startTimeEditingController;
  late TextEditingController _endTimeEditingController;
  late DateRangePickerController _dateController;

  @override
  void initState() {
    super.initState();
    _isSelectedLeft = true;
    _startDateEditingController = TextEditingController();
    _endDateEditingController = TextEditingController();
    _startTimeEditingController = TextEditingController();
    _endTimeEditingController = TextEditingController();
    _dateController = DateRangePickerController();

    _minDate = widget.limitMinDate;
    _maxDate = widget.limitMaxDate;
    _initSelectRange = widget.limitRange;

    if (_initSelectRange != null) {
      _startDate = DateFormat(
        'yyyy/MM/dd',
      ).format(_initSelectRange!.startDate!);
      _endDate = DateFormat('yyyy/MM/dd').format(_initSelectRange!.endDate!);
      _startTime = DateFormat('kk:mm').format(_initSelectRange!.startDate!);
      _endTime = DateFormat('kk:mm').format(_initSelectRange!.endDate!);
    }

    _startDateEditingController.text = _startDate;
    _endDateEditingController.text = _endDate;
    _startTimeEditingController.text = _startTime;
    _endTimeEditingController.text = _endTime;
  }

  @override
  void dispose() {
    _startDateEditingController.dispose();
    _endDateEditingController.dispose();
    _startTimeEditingController.dispose();
    _endTimeEditingController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.isSingle ? 400 : 800,
      height: widget.isSingle ? 340 : 500,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.5), blurRadius: 5),
        ],
      ),
      child: Column(
        children: [
          widget.isSingle
              ? Container()
              : Center(
                  child: Material(
                    color: Colors.transparent,
                    child: Row(
                      children: [
                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: TapRegion(
                              onTapInside: (e) {
                                if (_isVerify) {
                                  _selectTime(true);
                                  setState(() {});
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(
                                    Radius.circular(5),
                                  ),
                                  color: _isSelectedLeft
                                      ? Colors.blueAccent
                                      : Colors.transparent,
                                ),
                                padding: const EdgeInsets.all(10),
                                child: Column(
                                  children: [
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        widget.firstTitle,
                                        style: TextStyle(
                                          color: _isSelectedLeft
                                              ? Colors.white
                                              : Colors.black,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: CupertinoTextField(
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                RegExp(r'^[\d/]*$'),
                                              ), // Allow YYYY/MM/DD format
                                            ],
                                            placeholder: '请选择日期',
                                            controller:
                                                _startDateEditingController,
                                            onChanged: (value) {
                                              _isVerify = _validateDate(value);
                                              _startDate = value;
                                              if (_isVerify) {
                                                _setDateRange();
                                              }
                                              setState(() {});
                                            },
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Expanded(
                                          child: CupertinoTextField(
                                            key: _startTimeKey,
                                            placeholder: '请选择时间',
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                RegExp(r'^[\d:]*$'),
                                              ), // Allow YYYY/MM/DD format
                                            ],
                                            controller:
                                                _startTimeEditingController,
                                            onTap: () {
                                              var box =
                                                  _startTimeKey.currentContext!
                                                          .findRenderObject()
                                                      as RenderBox;
                                              var size = box.size;
                                              var position = box.localToGlobal(
                                                Offset.zero,
                                              );
                                              _showOverlay(
                                                size,
                                                position,
                                                true,
                                              );
                                            },
                                            onChanged: (value) {
                                              _isVerify = _validateTime(value);
                                              _startTime = value;
                                              if (_isVerify) {
                                                _overlayEntry?.remove();
                                                var box =
                                                    _startTimeKey
                                                            .currentContext!
                                                            .findRenderObject()
                                                        as RenderBox;
                                                var size = box.size;
                                                var position = box
                                                    .localToGlobal(Offset.zero);
                                                _showOverlay(
                                                  size,
                                                  position,
                                                  true,
                                                );
                                              }
                                              setState(() {});
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                    _isSelectedLeft
                                        ? Offstage(
                                            offstage:
                                                _isVerify && _isSelectedLeft,
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                top: 5,
                                              ),
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .info_outline_rounded,
                                                      size: 14,
                                                      color: _isSelectedLeft
                                                          ? Colors.white
                                                          : Colors.red,
                                                    ),
                                                    Text(
                                                      '日期/时间格式错误',
                                                      style: TextStyle(
                                                        color: _isSelectedLeft
                                                            ? Colors.white
                                                            : Colors.red,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          )
                                        : Container(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                          child: Icon(Icons.arrow_forward_ios_rounded),
                        ),
                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: TapRegion(
                              onTapInside: (e) {
                                if (_isVerify) {
                                  _selectTime(false);
                                  setState(() {});
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(
                                    Radius.circular(5),
                                  ),
                                  color: !_isSelectedLeft
                                      ? Colors.blueAccent
                                      : Colors.transparent,
                                ),
                                padding: const EdgeInsets.all(10),
                                child: Column(
                                  children: [
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        widget.lastTitle,
                                        style: TextStyle(
                                          color: !_isSelectedLeft
                                              ? Colors.white
                                              : Colors.black,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: CupertinoTextField(
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                RegExp(r'^[\d/]*$'),
                                              ), // Allow YYYY/MM/DD format
                                            ],
                                            placeholder: '请选择日期',
                                            controller:
                                                _endDateEditingController,
                                            onChanged: (value) {
                                              _isVerify = _validateDate(value);
                                              _endDate = value;
                                              if (_isVerify) {
                                                _setDateRange();
                                              }
                                              setState(() {});
                                            },
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Expanded(
                                          child: CupertinoTextField(
                                            key: _endTimeKey,
                                            placeholder: '请选择时间',
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                RegExp(r'^[\d:]*$'),
                                              ), // Allow YYYY/MM/DD format
                                            ],
                                            controller:
                                                _endTimeEditingController,
                                            onTap: () {
                                              var box =
                                                  _endTimeKey.currentContext!
                                                          .findRenderObject()
                                                      as RenderBox;
                                              var size = box.size;
                                              var position = box.localToGlobal(
                                                Offset.zero,
                                              );
                                              _showOverlay(
                                                size,
                                                position,
                                                false,
                                              );
                                            },
                                            onChanged: (value) {
                                              _isVerify = _validateTime(value);
                                              _endTime = value;
                                              if (_isVerify) {
                                                _overlayEntry?.remove();
                                                var box =
                                                    _endTimeKey.currentContext!
                                                            .findRenderObject()
                                                        as RenderBox;
                                                var size = box.size;
                                                var position = box
                                                    .localToGlobal(Offset.zero);
                                                _showOverlay(
                                                  size,
                                                  position,
                                                  false,
                                                );
                                              }
                                              setState(() {});
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                    _isSelectedLeft
                                        ? Container()
                                        : Offstage(
                                            offstage:
                                                _isVerify && !_isSelectedLeft,
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                top: 5,
                                              ),
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .info_outline_rounded,
                                                      size: 14,
                                                      color: !_isSelectedLeft
                                                          ? Colors.white
                                                          : Colors.red,
                                                    ),
                                                    Text(
                                                      '日期/时间格式错误',
                                                      style: TextStyle(
                                                        color: !_isSelectedLeft
                                                            ? Colors.white
                                                            : Colors.red,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
          TapRegion(
            onTapInside: (event) {
              _isSelectingDate = true;
            },
            onTapOutside: (event) {
              _isSelectingDate = false;
            },
            child: SfDateRangePicker(
              enableMultiView: widget.isSingle ? false : true,
              controller: _dateController,
              allowViewNavigation: false,
              showActionButtons: true,
              showNavigationArrow: true,
              confirmText: '确认',
              cancelText: '重置',
              minDate: _minDate,
              maxDate: _maxDate,
              // initialSelectedRange: _initSelectRange,
              //todo该属性在初始的时间选择范围为同一天的情况下出现严重问题，先禁用
              onSelectionChanged: (arg) {
                if (widget.isSingle) {
                  _startDate = DateFormat('yyyy/MM/dd').format(arg.value);
                  _endDate = DateFormat('yyyy/MM/dd').format(arg.value);
                  _startTime = '18:30';
                  _endTime = '18:30';
                } else {
                  if (_isSelectingDate) {
                    if (arg.value is PickerDateRange) {
                      if (_startDate != '' && _endDate != '') {
                        _isSelectedLeft = true;
                        _endDate = '';
                        _endTime = '';
                        _endDateEditingController.text = _endDate;
                        _endTimeEditingController.text = _endTime;
                      }
                      if (_isSelectedLeft) {
                        _startDate = DateFormat(
                          'yyyy/MM/dd',
                        ).format(arg.value.startDate);
                        _startDateEditingController.text = _startDate;
                        _startTime = '9:00';
                        _startTimeEditingController.text = _startTime;
                        _isSelectedLeft = false;
                        var startStrs = _startDate.split('/');
                        _minDate = DateTime(
                          int.parse(startStrs[0]),
                          int.parse(startStrs[1]),
                          int.parse(startStrs[2]),
                          9,
                        );
                        setState(() {});
                      } else {
                        _endDate = DateFormat(
                          'yyyy/MM/dd',
                        ).format(arg.value.endDate);
                        _endDateEditingController.text = _endDate;
                        _endTime = '18:30';
                        _endTimeEditingController.text = _endTime;
                        _minDate = widget.limitMinDate;
                        setState(() {});
                      }
                    }
                  }
                }
              },
              headerStyle: const DateRangePickerHeaderStyle(
                backgroundColor: Colors.transparent,
              ),
              backgroundColor: Colors.transparent,
              selectionMode: widget.isSingle
                  ? DateRangePickerSelectionMode.single
                  : DateRangePickerSelectionMode.range,
              onCancel: () {
                _isSelectedLeft = true;
                setState(() {});
              },
              onSubmit: (p0) {
                var temps = _startDate.split('/');
                var temps1 = _startTime.split(':');
                var startDate = DateTime(
                  int.parse(temps[0]),
                  int.parse(temps[1]),
                  int.parse(temps[2]),
                  int.parse(temps1[0]),
                  int.parse(temps1[1]),
                );
                temps = _endDate.split('/');
                temps1 = _endTime.split(':');
                var endDate = DateTime(
                  int.parse(temps[0]),
                  int.parse(temps[1]),
                  int.parse(temps[2]),
                  int.parse(temps1[0]),
                  int.parse(temps1[1]),
                );
                var reslutDate = PickerDateRange(startDate, endDate);
                widget.onSubmited(reslutDate);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showOverlay(Size size, Offset position, bool isStartTime) {
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          top: position.dy + size.height - 5,
          left: position.dx + 1,
          child: TapRegion(
            onTapInside: (event) {
              _overlayEntry?.remove();
              widget.onSelecting(false);
            },
            onTapOutside: (event) {
              _overlayEntry?.remove();
              widget.onSelecting(false);
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(5),
                  bottomRight: Radius.circular(5),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 5,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(20),
              width: size.width - 1,
              height: 300,
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.time,
                initialDateTime: _setTime(isStartTime),
                use24hFormat: true,
                itemExtent: 70,
                onDateTimeChanged: (value) {
                  if (isStartTime) {
                    if (value.minute < 10) {
                      _startTime = '${value.hour}:0${value.minute}';
                    } else {
                      _startTime = '${value.hour}:${value.minute}';
                    }
                    _startTimeEditingController.text = _startTime;
                  } else {
                    if (value.minute < 10) {
                      _endTime = '${value.hour}:0${value.minute}';
                    } else {
                      _endTime = '${value.hour}:${value.minute}';
                    }
                    _endTimeEditingController.text = _endTime;
                  }
                  setState(() {});
                },
              ),
            ),
          ),
        );
      },
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  bool _validateDate(String value) {
    var dateRegExp = RegExp(
      '^(?:(?!0000)[0-9]{4}([-/.]?)(?:(?:0?[1-9]|1[0-2])\\1(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])\\1(?:29|30)|(?:0?[13578]|1[02])\\1(?:31))|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)([-/.]?)0?2\\2(?:29))\$',
    );
    var result = dateRegExp.hasMatch(value); //结果为true则正确,反正则不正确
    return result;
  }

  bool _validateTime(String value) {
    var dateRegExp = RegExp('^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9]\$');
    var result = dateRegExp.hasMatch(value); //结果为true则正确,反正则不正确
    return result;
  }

  void _setDateRange() {
    var startdatetemps = _startDate.split('/');
    var starttemps = _startTime.split(':');
    var startDateTime = DateTime(
      int.parse(startdatetemps[0]),
      int.parse(startdatetemps[1]),
      int.parse(startdatetemps[2]),
      int.parse(starttemps[0]),
      int.parse(starttemps[1]),
    );

    var enddatetemps = _endDate.split('/');
    var endtemps = _endTime.split(':');
    var endDateTime = DateTime(
      int.parse(enddatetemps[0]),
      int.parse(enddatetemps[1]),
      int.parse(enddatetemps[2]),
      int.parse(endtemps[0]),
      int.parse(endtemps[1]),
    );

    _dateController.selectedRange = PickerDateRange(startDateTime, endDateTime);
  }

  DateTime _setTime(bool isStartTime) {
    widget.onSelecting(true);
    DateTime? initTime;
    if (isStartTime) {
      if (_startTime != '') {
        var time = _startDate.split('/');
        var temps = _startTime.split(':');
        initTime = DateTime(
          int.parse(time[0]),
          int.parse(time[1]),
          int.parse(time[2]),
          int.parse(temps[0]),
          int.parse(temps[1]),
        );
      }
    } else {
      if (_endTime != '') {
        var time = _endDate.split('/');
        var temps = _endTime.split(':');
        initTime = DateTime(
          int.parse(time[0]),
          int.parse(time[1]),
          int.parse(time[2]),
          int.parse(temps[0]),
          int.parse(temps[1]),
        );
      }
    }
    return initTime!;
  }

  void _selectTime(bool isSelectLeft) {
    _isSelectedLeft = isSelectLeft;
  }
}
