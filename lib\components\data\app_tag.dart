import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';
import 'package:jyt_components_package/utils/icon_font.dart';

class AppTag extends StatelessWidget {
  /// dark 背景色模式；plain 边框模式
  final String effect; //plain / dark

  /// 显示文本
  final String text;

  /// 标签颜色
  final Color color;

  final Color? textColor;

  /// 是否显示关闭按钮
  final bool closable;

  /// 标签高度
  final double height;

  /// 关闭按钮点击事件
  final VoidCallback? close;

  const AppTag({
    super.key,
    this.effect = 'dark',
    this.text = '',
    this.color = AppColors.primary,
    this.textColor,
    this.closable = false,
    this.height = 20.0,
    this.close,
  });

  @override
  Widget build(BuildContext context) {
    double closeBtnWidth = 12;

    return Container(
      height: height,
      constraints: BoxConstraints(maxWidth: 200),
      decoration: BoxDecoration(
        color: effect == 'dark' ? color : null,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
        border: effect == 'plain' ? Border.all(color: color, width: 1.0) : null,
      ),
      padding: EdgeInsets.only(left: 8, right: 8, bottom: 1),

      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                color:
                    textColor ?? (effect == 'dark' ? AppColors.textWhite : AppColors.textPrimary),
                fontSize: 12,
              ),
              overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
              maxLines: 1, // 单行显示
            ),
          ),

          if (closable) ...[
            SizedBox(width: 4),
            Material(
              color: effect == 'dark' ? color : null,
              child: InkWell(
                onTap: () => close?.call(),
                child: Icon(
                  IconFont.xianxing_guanbi,
                  size: closeBtnWidth,
                  color: effect == 'dark' ? AppColors.textWhite : context.icon300,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
